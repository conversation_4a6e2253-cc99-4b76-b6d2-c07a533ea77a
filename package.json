{"name": "@gus-eip/platform-listener", "version": "2.0.7", "description": "@gus-eip/platform-listener is a package designed to provide platform event listening functionality for your Node.js applications.", "author": "gus", "readmeFilename": "README.md", "main": "dist/index.js", "files": ["dist/**/*", "*.md"], "scripts": {"start:dev": "tsc -w", "build": "tsc", "prepare": "npm run build", "format": "prettier --write \"src/**/*.ts\"", "lint": "tslint -p tsconfig.json -c tslint.json", "test": "node --experimental-vm-modules ./node_modules/.bin/jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:e2e": "jest --config ./test/jest-e2e.json"}, "keywords": ["<PERSON><PERSON><PERSON>", "nodejs", "javascript", "typescript", "@gus-eip/platform-listener"], "publishConfig": {"access": "public"}, "dependencies": {"@types/lodash": "^4.17.7", "aws-sdk": "^2.1590.0", "cometd": "7.0.12", "cometd-nodejs-client": "^1.5.0", "lodash": "^4.17.21", "uuid": "^10.0.0", "salesforce-event-listener": "git+https://github.com/Logesh883/pubsub-listener.git"}, "devDependencies": {"@nestjs/common": "^10.0.2", "@nestjs/core": "^10.0.2", "@nestjs/platform-express": "^10.0.2", "@nestjs/testing": "10.0.2", "@types/express": "4.17.17", "@types/jest": "29.5.2", "@types/node": "20.3.1", "@types/supertest": "2.0.12", "@typescript-eslint/eslint-plugin": "^5.60.0", "@typescript-eslint/parser": "^5.60.0", "eslint": "^8.43.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "29.5.0", "prettier": "2.8.8", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "supertest": "6.3.3", "ts-jest": "29.1.0", "ts-node": "10.9.1", "tsc-watch": "6.0.4", "tsconfig-paths": "4.2.0", "tslint": "5.20.1", "typescript": "5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}}