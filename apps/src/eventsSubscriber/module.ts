import { Module, OnModuleInit } from '@nestjs/common';
import { PlatformEventSubscriberService } from './service';
import { PlatformEventSubscriberController } from './controller';
import { SalesforceAuthService } from '../common/salesforceAuth';
import { DynamoDBService } from '../common/dynamodb.service';
import { PlatformEventModule } from '@gus-eip/platform-listener';
import { ConfigModule } from '@nestjs/config';
import { LoggerModule } from '@gus-eip/loggers';
import { SqsService } from '../common/sqs.service';
import { SnsService } from '../common/sns.service';
import { SslConfigService } from '../common/ssl-config.service';
import { GrpcSslFixService } from '../common/grpc-ssl-fix.service';
const ENV = process.env.NODE_ENV || 'dev';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ENV === 'prod' ? '.env' : `.env.${ENV}`,
    }),
    LoggerModule.forRoot({
      region: process.env.REGION,
      logGroupName: process.env.LOGGER_LOG_GROUP_NAME,
      teamWebhookUrl: process.env.TEAMS_WEBHOOK_URL,
      isAlertNeeded: true,
      options: 'CloudWatchLogger',
    }),
    PlatformEventModule.forRoot({
      username: process.env.GUS_SALESFORCE_USERNAME,
      password: process.env.GUS_SALESFORCE_PASSWORD,
      clientId: process.env.GUS_SALESFORCE_CLIENT_ID,
      clientSecret: process.env.GUS_SALESFORCE_CLIENT_SECRET,
      authUrl: process.env.GUS_SALESFORCE_URL,
      options: 'PlatformEventListener',
    }),
  ],
  providers: [
    PlatformEventSubscriberService,
    SalesforceAuthService,
    DynamoDBService,
    SqsService,
    SnsService,
    SslConfigService,
    GrpcSslFixService,
  ],
  controllers: [PlatformEventSubscriberController],
})
export class PlatformEventSubscriberModule implements OnModuleInit {
  constructor(
    private readonly platformEventSubscriberService: PlatformEventSubscriberService,
  ) {}

  async onModuleInit() {
    // Start the Lead Owner Change Event listener
    const leadOwnerEventName = 'Lead_Owner_Change_Event__e';
    try {
      await this.platformEventSubscriberService.platformlistener(
        leadOwnerEventName,
      );
      console.log(`Successfully subscribed to ${leadOwnerEventName}`);
    } catch (error) {
      console.error(
        `Failed to subscribe to ${leadOwnerEventName}`,
        error.stack,
      );
    }

    // Start the Application Change Notification Event listener
    const applicationChangeEventName = 'Application_Change_Notification__e';
    try {
      await this.platformEventSubscriberService.applicationChangeListener(
        applicationChangeEventName,
      );
      console.log(`Successfully subscribed to ${applicationChangeEventName}`);
    } catch (error) {
      console.error(
        `Failed to subscribe to ${applicationChangeEventName}`,
        error.stack,
      );
    }
  }
}
