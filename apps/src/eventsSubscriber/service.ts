import { Inject, Injectable } from '@nestjs/common';
import { SalesforceAuthService } from '../common/salesforceAuth';
import { DynamoDBService } from '../common/dynamodb.service';
import { SqsService } from '../common/sqs.service';
import { CloudWatchLoggerService, LoggerEnum } from '@gus-eip/loggers';
import { PlatformEventService } from '@gus-eip/platform-listener';
import axios from 'axios';
import {
  createAlertMessageCard,
  createRetryMessageCard,
  createGrpcKeepAliveFailureCard,
} from './templates/alertMessageCard';
import { SnsService } from '../common/sns.service';

// Interface for listener-specific retry state
interface ListenerRetryState {
  retryCount: number;
  retryInProgress: boolean;
  lastReplayId: number | null;
  maxRetryAttempts: number;
  baseDelay: number;
  isActive: boolean;
  lastGrpcKeepAliveTime: Date;
  grpcKeepAliveCheckInterval: NodeJS.Timeout | null;
  keepaliveAlertState: {
    isInFailureMode: boolean;
    firstFailureTime: Date | null;
    lastAlertSentTime: Date | null;
    alertCount: number;
  };
}

// Interface for listener configuration
interface ListenerConfig {
  maxRetryAttempts: number;
  baseDelay: number;
  grpcKeepAliveTimeoutMs: number;
  keepAliveAlertFirstIntervalMs: number;
  keepAliveAlertSubsequentIntervalMs: number;
}

@Injectable()
export class PlatformEventSubscriberService {
  // Global service state (for backward compatibility)
  private lastProcessedReplayId: number | null = null;

  // Configuration constants
  private readonly GRPC_KEEPALIVE_TIMEOUT_MS = 3 * 60 * 1000; // 3 minute
  private readonly KEEPALIVE_ALERT_FIRST_INTERVAL_MS = 1 * 60 * 1000; // 1 minute
  private readonly KEEPALIVE_ALERT_SUBSEQUENT_INTERVAL_MS = 60 * 60 * 1000; // 1 hour

  // Independent state for each listener
  private readonly listenerStates: Map<string, ListenerRetryState> = new Map();

  // Default configuration for listeners
  private readonly defaultListenerConfig: ListenerConfig = {
    maxRetryAttempts: 5,
    baseDelay: 2000,
    grpcKeepAliveTimeoutMs: 1 * 60 * 1000, // 1 minute
    keepAliveAlertFirstIntervalMs: 1 * 60 * 1000, // 1 minute
    keepAliveAlertSubsequentIntervalMs: 60 * 60 * 1000, // 1 hour
  };

  // Legacy keepalive alert state (for backward compatibility)
  private keepaliveAlertState: {
    isInFailureMode: boolean;
    firstFailureTime: Date | null;
    lastAlertSentTime: Date | null;
    alertCount: number;
  } = {
    isInFailureMode: false,
    firstFailureTime: null,
    lastAlertSentTime: null,
    alertCount: 0,
  };

  constructor(
    @Inject('CloudWatchLogger')
    private readonly cloudWatchLoggerService: CloudWatchLoggerService,
    private readonly loggerEnum: LoggerEnum,
    @Inject('PlatformEventListener')
    private readonly platformEventService: PlatformEventService,
    private authService: SalesforceAuthService,
    private dynamodbService: DynamoDBService,
    private sqsService: SqsService,
    private snsService: SnsService,
  ) {}

  /**
   * Initialize listener state for a specific platform event
   */
  private initializeListenerState(
    platformEventName: string,
    config?: Partial<ListenerConfig>,
  ): ListenerRetryState {
    const finalConfig = { ...this.defaultListenerConfig, ...config };

    const state: ListenerRetryState = {
      retryCount: 0,
      retryInProgress: false,
      lastReplayId: null,
      maxRetryAttempts: finalConfig.maxRetryAttempts,
      baseDelay: finalConfig.baseDelay,
      isActive: false,
      lastGrpcKeepAliveTime: new Date(),
      grpcKeepAliveCheckInterval: null,
      keepaliveAlertState: {
        isInFailureMode: false,
        firstFailureTime: null,
        lastAlertSentTime: null,
        alertCount: 0,
      },
    };

    this.listenerStates.set(platformEventName, state);
    return state;
  }

  /**
   * Get listener state for a specific platform event
   */
  private getListenerState(platformEventName: string): ListenerRetryState {
    let state = this.listenerStates.get(platformEventName);
    if (!state) {
      state = this.initializeListenerState(platformEventName);
    }
    return state;
  }

  /**
   * Calculate exponential backoff delay for retries
   */
  private calculateRetryDelay(retryCount: number, baseDelay: number): number {
    return baseDelay * Math.pow(2, retryCount - 1);
  }

  /**
   * Reset retry state for a listener
   */
  private resetRetryState(platformEventName: string): void {
    const state = this.getListenerState(platformEventName);
    state.retryCount = 0;
    state.retryInProgress = false;
  }

  async sendWebhookNotification(messageCard: any) {
    try {
      const webhookUrl = process.env.TEAMS_WEBHOOK_URL;

      if (!webhookUrl) {
        console.error('Webhook URL is not configured');
        return;
      }

      const response = await axios.post(webhookUrl, messageCard);

      if (response.status !== 200) {
        console.error(
          'Failed to send webhook notification:',
          response.statusText,
        );
      } else {
        console.log('Webhook notification sent successfully');
      }
    } catch (error) {
      console.error('Error sending webhook notification:', error);
    }
  }

  async sendKeepAliveStatus(messageCard: any) {
    try {
      const webhookUrl = process.env.TEAMS_HEALTH_WEBHOOK_URL;

      if (!webhookUrl) {
        console.error('Webhook URL is not configured');
        return;
      }

      const response = await axios.post(webhookUrl, messageCard);

      if (response.status !== 200) {
        console.error(
          'Failed to send webhook notification:',
          response.statusText,
        );
      } else {
        console.log('Webhook notification sent successfully');
      }
    } catch (error) {
      console.error('Error sending webhook notification:', error);
    }
  }

  private async getKeepAliveAlertState(
    platformEventName: string,
  ): Promise<any> {
    try {
      const result = await this.dynamodbService.getObject(
        process.env.OAP_EVENT_TABLE_NAME,
        { PK: `KEEPALIVE_STATE_${platformEventName}` },
      );

      if (result.Item) {
        return {
          isInFailureMode: result.Item.isInFailureMode || false,
          firstFailureTime: result.Item.firstFailureTime
            ? new Date(result.Item.firstFailureTime)
            : null,
          lastAlertSentTime: result.Item.lastAlertSentTime
            ? new Date(result.Item.lastAlertSentTime)
            : null,
          alertCount: result.Item.alertCount || 0,
        };
      }

      return {
        isInFailureMode: false,
        firstFailureTime: null,
        lastAlertSentTime: null,
        alertCount: 0,
      };
    } catch (error) {
      console.error('Error getting keepalive alert state:', error);
      return {
        isInFailureMode: false,
        firstFailureTime: null,
        lastAlertSentTime: null,
        alertCount: 0,
      };
    }
  }

  private async saveKeepAliveAlertState(
    platformEventName: string,
    state: any,
  ): Promise<void> {
    try {
      await this.dynamodbService.updateObject(
        process.env.OAP_EVENT_TABLE_NAME,
        { PK: `KEEPALIVE_STATE_${platformEventName}` },
        {
          isInFailureMode: state.isInFailureMode,
          firstFailureTime: state.firstFailureTime
            ? state.firstFailureTime.toISOString()
            : null,
          lastAlertSentTime: state.lastAlertSentTime
            ? state.lastAlertSentTime.toISOString()
            : null,
          alertCount: state.alertCount,
          updatedAt: new Date().toISOString(),
          expirationDate: new Date(
            Date.now() + 7 * 24 * 60 * 60 * 1000,
          ).toISOString(), // 7 days TTL
        },
      );
    } catch (error) {
      console.error('Error saving keepalive alert state:', error);
    }
  }

  private async clearKeepAliveAlertState(
    platformEventName: string,
  ): Promise<void> {
    try {
      await this.saveKeepAliveAlertState(platformEventName, {
        isInFailureMode: false,
        firstFailureTime: null,
        lastAlertSentTime: null,
        alertCount: 0,
      });
    } catch (error) {
      console.error('Error clearing keepalive alert state:', error);
    }
  }

  private startGrpcKeepAliveMonitoring(platformEventName: string) {
    const state = this.getListenerState(platformEventName);

    // Clear existing interval if any
    if (state.grpcKeepAliveCheckInterval) {
      clearInterval(state.grpcKeepAliveCheckInterval);
    }

    // Check for gRPC keep-alive timeout every minute
    state.grpcKeepAliveCheckInterval = setInterval(async () => {
      await this.checkGrpcKeepAliveTimeout(platformEventName);
    }, 60 * 1000); // Check every minute

    console.log(`gRPC Keep-Alive monitoring started for ${platformEventName}`);
  }

  private async checkGrpcKeepAliveTimeout(platformEventName: string) {
    try {
      const state = this.getListenerState(platformEventName);
      const now = new Date();
      const timeSinceLastKeepAlive =
        now.getTime() - state.lastGrpcKeepAliveTime.getTime();

      if (timeSinceLastKeepAlive > this.GRPC_KEEPALIVE_TIMEOUT_MS) {
        await this.handleKeepAliveFailure(
          platformEventName,
          now,
          timeSinceLastKeepAlive,
        );
      } else {
        // Connection is healthy, clear any failure state
        await this.handleKeepAliveRecovery(platformEventName);
      }
    } catch (error) {
      console.error(
        `Error checking gRPC keep-alive timeout for ${platformEventName}:`,
        error,
      );
    }
  }

  private async handleKeepAliveFailure(
    platformEventName: string,
    now: Date,
    timeSinceLastKeepAlive: number,
  ) {
    try {
      const state = this.getListenerState(platformEventName);
      // Get current alert state from database
      const alertState = await this.getKeepAliveAlertState(platformEventName);

      let shouldSendAlert = false;

      if (!alertState.isInFailureMode) {
        // First time detecting failure
        alertState.isInFailureMode = true;
        alertState.firstFailureTime = now;
        alertState.alertCount = 0;
        shouldSendAlert = true;
        console.log(
          `First keepalive failure detected for ${platformEventName}, sending immediate alert`,
        );
      } else {
        // Already in failure mode, check if we should send another alert
        const timeSinceLastAlert = alertState.lastAlertSentTime
          ? now.getTime() - alertState.lastAlertSentTime.getTime()
          : Number.MAX_SAFE_INTEGER;

        if (alertState.alertCount === 0) {
          // First alert hasn't been sent yet
          shouldSendAlert = true;
        } else if (
          timeSinceLastAlert >= this.KEEPALIVE_ALERT_SUBSEQUENT_INTERVAL_MS
        ) {
          // Enough time has passed for subsequent alert (1 hour)
          shouldSendAlert = true;
          console.log(
            `Sending subsequent keepalive failure alert for ${platformEventName} (1 hour interval)`,
          );
        }
      }

      if (shouldSendAlert) {
        const minutesSinceLastKeepAlive = Math.floor(
          timeSinceLastKeepAlive / (1000 * 60),
        );

        const alertCard = createGrpcKeepAliveFailureCard(
          platformEventName,
          state.lastReplayId?.toString() || 'No replay ID available',
          state.lastGrpcKeepAliveTime.toLocaleString('en-IN', {
            timeZone: 'Asia/Kolkata',
          }),
          minutesSinceLastKeepAlive,
        );

        await this.sendKeepAliveStatus(alertCard);

        // Update alert state
        alertState.lastAlertSentTime = now;
        alertState.alertCount += 1;
        await this.saveKeepAliveAlertState(platformEventName, alertState);

        console.log(
          `gRPC Keep-Alive failure alert sent for ${platformEventName} - ${minutesSinceLastKeepAlive} minutes since last keep-alive (Alert #${alertState.alertCount})`,
        );

        // Only exit on first failure to allow ECS restart
        if (alertState.alertCount === 1) {
          console.log('Exiting process to allow ECS to restart the task...');
          process.exit(1);
        }
      }
    } catch (error) {
      console.error(
        `Error checking gRPC keep-alive timeout for ${platformEventName}:`,
        error,
      );
    }
  }

  private async handleKeepAliveRecovery(platformEventName: string) {
    try {
      const alertState = await this.getKeepAliveAlertState(platformEventName);

      if (alertState.isInFailureMode) {
        console.log(
          `Keepalive connection recovered for ${platformEventName}, clearing failure state`,
        );
        await this.clearKeepAliveAlertState(platformEventName);
      }
    } catch (error) {
      console.error(
        `Error handling keepalive recovery for ${platformEventName}:`,
        error,
      );
    }
  }

  private stopMonitoring(platformEventName: string) {
    const state = this.getListenerState(platformEventName);

    if (state.grpcKeepAliveCheckInterval) {
      clearInterval(state.grpcKeepAliveCheckInterval);
      state.grpcKeepAliveCheckInterval = null;
    }

    state.isActive = false;
    console.log(`Simplified monitoring stopped for ${platformEventName}`);
  }

  getListenerStatus(platformEventName?: string) {
    if (platformEventName) {
      const state = this.getListenerState(platformEventName);
      return {
        platformEventName,
        isActive: state.isActive,
        lastGrpcKeepAliveTime: state.lastGrpcKeepAliveTime,
        lastProcessedReplayId: state.lastReplayId,
        retryCount: state.retryCount,
        retryInProgress: state.retryInProgress,
      };
    }

    // Return status for all listeners
    const allStatuses: any = {};
    for (const [eventName, state] of this.listenerStates.entries()) {
      allStatuses[eventName] = {
        isActive: state.isActive,
        lastGrpcKeepAliveTime: state.lastGrpcKeepAliveTime,
        lastProcessedReplayId: state.lastReplayId,
        retryCount: state.retryCount,
        retryInProgress: state.retryInProgress,
      };
    }
    return allStatuses;
  }

  async platformlistener(platformEventName: string): Promise<void> {
    try {
      // Initialize listener state
      const state = this.getListenerState(platformEventName);
      state.isActive = true;
      state.lastGrpcKeepAliveTime = new Date();

      // Start simplified monitoring systems
      this.startGrpcKeepAliveMonitoring(platformEventName);
      state.keepaliveAlertState =
        await this.getKeepAliveAlertState(platformEventName);
      console.log(
        `Loaded keepalive alert state for ${platformEventName}:`,
        state.keepaliveAlertState,
      );

      state.lastReplayId = await this.getLastReplayId(platformEventName);
      console.log(
        `lastReplayId for ${platformEventName} ->`,
        state.lastReplayId,
      );

      // Reset retry state for this listener
      this.resetRetryState(platformEventName);

      const subscribeCallback = async (
        subscription: any,
        callbackType: any,
        eventData: any,
      ) => {
        const currentState = this.getListenerState(platformEventName);

        if (callbackType === 'grpcKeepAlive' || callbackType === 'event') {
          currentState.lastGrpcKeepAliveTime = new Date();

          // Handle keepalive recovery - clear any failure state
          await this.handleKeepAliveRecovery(platformEventName);

          if (currentState.retryInProgress) {
            const successCard = createRetryMessageCard(
              platformEventName,
              currentState.lastReplayId ||
                'No replayId found in DB after retried',
              currentState.retryCount,
              true,
            );
            await this.sendWebhookNotification(successCard);
            currentState.retryInProgress = false;
          }
          currentState.retryCount = 0;
        }

        if (callbackType === 'event') {
          console.log(
            `${subscription?.topicName} - ${subscription?.receivedEventCount} event(s) received on the channel.`,
          );
        }

        if (eventData && callbackType === 'event') {
          const replayId = eventData.event.replayId;
          if (replayId > currentState.lastReplayId) {
            currentState.lastReplayId = eventData.event.replayId;

            await this.postMessageToSQS(JSON.parse(JSON.stringify(eventData)));
          } else {
            console.log('Duplicate', replayId, currentState.lastReplayId);
          }
        } else if (callbackType === 'end') {
          currentState.lastReplayId =
            await this.getLastReplayId(platformEventName);

          if (currentState.retryCount < currentState.maxRetryAttempts) {
            currentState.retryCount++;
            currentState.retryInProgress = true;

            const delayMs = this.calculateRetryDelay(
              currentState.retryCount,
              currentState.baseDelay,
            );
            console.log(
              `[${platformEventName}] Waiting ${delayMs}ms before retry attempt ${currentState.retryCount}`,
            );

            if (currentState.retryCount === 1) {
              const messageCard = createRetryMessageCard(
                platformEventName,
                currentState.lastReplayId ||
                  'No replayId found in DB while retrying. So begin with new replayId',
                currentState.retryCount,
                false,
              );
              await this.sendWebhookNotification(messageCard);
            }

            console.log(
              `[${platformEventName}] Retry attempt ${currentState.retryCount} of ${currentState.maxRetryAttempts}`,
            );

            await new Promise((resolve) => setTimeout(resolve, delayMs));

            await this.platformEventService.gRCPPlatformEventListener(
              platformEventName,
              subscribeCallback,
              currentState.lastReplayId,
            );
          } else {
            console.log(
              `[${platformEventName}] Max retry attempts (${currentState.maxRetryAttempts}) exceeded. Stopping retries.`,
            );

            const messageCard = createAlertMessageCard(
              platformEventName,
              currentState.lastReplayId,
            );

            await this.sendWebhookNotification(messageCard);

            console.log('Exiting process to allow ECS to restart the task...');
            process.exit(1); // Exit with error code 1 to indicate failure
          }
        }
      };

      await this.platformEventService.gRCPPlatformEventListener(
        platformEventName,
        subscribeCallback,
        state.lastReplayId,
      );
    } catch (error) {
      console.error(
        `Critical error in platform listener for ${platformEventName}:`,
        error,
      );

      // Stop monitoring
      this.stopMonitoring(platformEventName);
    }
  }

  async postMessageToSQS(eventData: any) {
    try {
      if (eventData.event.replayId > this.lastProcessedReplayId) {
        await this.dynamodbService.updateObject(
          process.env.OAP_EVENT_TABLE_NAME,
          {
            PK: eventData.event.EventApiName,
          },
          {
            replayId: eventData.event.replayId,
            event: eventData.event,
            createdDate: new Date(eventData.payload.CreatedDate).toISOString(),
            expirationDate: new Date(
              eventData.payload.CreatedDate + 24 * 60 * 60 * 1000,
            ).toISOString(),
          },
        );

        const publishMessageResponse = await this.sqsService.sendMessage(
          eventData,
          process.env.GUS_SF_QUEUE_URL,
          0,
          eventData.payload.Email__c,
        );
        this.lastProcessedReplayId = eventData.event.replayId;

        console.log(`Last processed replayId: ${this.lastProcessedReplayId}`);

        console.log(
          'Publish Message Response -->',
          JSON.stringify(publishMessageResponse),
        );
        if (publishMessageResponse.MessageId) {
          await this.cloudWatchLoggerService.log(
            eventData.event?.EventUuid,
            new Date().toISOString(),
            this.loggerEnum.Component.GUS_SALESFORCE_EVENTS_LISTENER,
            this.loggerEnum.Component.GUS_SALESFORCE,
            'OAP_LEAD_ASSIGNMENT_QUEUE',
            this.loggerEnum.Event.SYNC_APPLICATION_DETAILS,
            eventData.payload.Scenario__c,
            eventData,
            eventData,
            'Message published to SQS',
            eventData.payload?.BusinessUnitFilter__c,
            eventData.payload?.Email__c,
            `gus-salesforce-event-listener/${eventData.event?.EventUuid}/${eventData.payload?.Email__c}`,
            'email',
            eventData.payload?.Email__c,
            'Opportunity_Application_Account_Lead',
            '',
            'Contact_Opportunity_Application',
            eventData.payload?.Email__c,
            publishMessageResponse,
          );
        }
      } else {
        console.log(
          `Skipping event with replayId: ${eventData.event.replayId}`,
        );
      }
    } catch (error) {
      console.error('Error in platform listener:', error);
      await this.cloudWatchLoggerService.error(
        eventData.event?.EventUuid,
        new Date().toISOString(),
        'GUS_SALESFORCE_EVENTS_LISTENER',
        'GUS_SALESFORCE',
        'OAP_LEAD_ASSIGNMENT_QUEUE',
        'SYNC_APPLICATION_DETAILS',
        'OAP LEAD ASSIGNMENT',
        eventData,
        eventData,
        error.message ? error.message : error,
        eventData.payload?.BusinessUnitFilter__c,
        eventData.payload?.Email__c,
        `gus-salesforce-event-listener/${eventData.event?.EventUuid}/${eventData.payload?.Email__c}`,
        'email',
        eventData.payload?.Email__c,
        'Opportunity_Application_Account_Lead',
        '',
        'Contact_Opportunity_Application',
        eventData.payload?.Email__c,
      );
    }
  }
  async getLastReplayId(platformEventName: string) {
    const getLastReplayId = await this.dynamodbService.getObject(
      process.env.OAP_EVENT_TABLE_NAME,
      { PK: platformEventName },
    );

    if (
      !getLastReplayId.Item ||
      new Date().toISOString() > getLastReplayId.Item.expirationDate
    ) {
      return null;
    }
    const lastReplayId = getLastReplayId.Item.replayId;
    return lastReplayId;
  }

  async applicationChangeListener(platformEventName: string): Promise<void> {
    try {
      // Initialize listener state
      const state = this.getListenerState(platformEventName);
      state.isActive = true;
      state.lastGrpcKeepAliveTime = new Date();

      // Start simplified monitoring systems
      this.startGrpcKeepAliveMonitoring(platformEventName);
      state.keepaliveAlertState =
        await this.getKeepAliveAlertState(platformEventName);
      console.log(
        `Loaded keepalive alert state for ${platformEventName}:`,
        state.keepaliveAlertState,
      );

      state.lastReplayId = await this.getLastReplayId(platformEventName);
      console.log(
        `lastReplayId for ${platformEventName} ->`,
        state.lastReplayId,
      );

      // Reset retry state for this listener
      this.resetRetryState(platformEventName);

      const subscribeCallback = async (
        subscription: any,
        callbackType: any,
        eventData: any,
      ) => {
        const currentState = this.getListenerState(platformEventName);

        if (callbackType === 'grpcKeepAlive' || callbackType === 'event') {
          currentState.lastGrpcKeepAliveTime = new Date();

          // Handle keepalive recovery - clear any failure state
          await this.handleKeepAliveRecovery(platformEventName);

          if (currentState.retryInProgress) {
            const successCard = createRetryMessageCard(
              platformEventName,
              currentState.lastReplayId ||
                'No replayId found in DB after retried',
              currentState.retryCount,
              true,
            );
            await this.sendWebhookNotification(successCard);
            currentState.retryInProgress = false;
          }
          currentState.retryCount = 0;
        }

        if (callbackType === 'event') {
          console.log(
            `${subscription?.topicName} - ${subscription?.receivedEventCount} event(s) received on the channel.`,
          );
        }

        if (eventData && callbackType === 'event') {
          const replayId = eventData.event.replayId;
          if (replayId > currentState.lastReplayId) {
            currentState.lastReplayId = eventData.event.replayId;

            await this.publishMessageToSNS(
              JSON.parse(JSON.stringify(eventData)),
            );
          } else {
            console.log('Duplicate', replayId, currentState.lastReplayId);
          }
        } else if (callbackType === 'end') {
          currentState.lastReplayId =
            await this.getLastReplayId(platformEventName);

          if (currentState.retryCount < currentState.maxRetryAttempts) {
            currentState.retryCount++;
            currentState.retryInProgress = true;

            const delayMs = this.calculateRetryDelay(
              currentState.retryCount,
              currentState.baseDelay,
            );
            console.log(
              `[${platformEventName}] Waiting ${delayMs}ms before retry attempt ${currentState.retryCount}`,
            );

            if (currentState.retryCount === 1) {
              const messageCard = createRetryMessageCard(
                platformEventName,
                currentState.lastReplayId ||
                  'No replayId found in DB while retrying. So begin with new replayId',
                currentState.retryCount,
                false,
              );
              await this.sendWebhookNotification(messageCard);
            }

            console.log(
              `[${platformEventName}] Retry attempt ${currentState.retryCount} of ${currentState.maxRetryAttempts}`,
            );

            await new Promise((resolve) => setTimeout(resolve, delayMs));

            await this.platformEventService.gRCPPlatformEventListener(
              platformEventName,
              subscribeCallback,
              currentState.lastReplayId,
            );
          } else {
            console.log(
              `[${platformEventName}] Max retry attempts (${currentState.maxRetryAttempts}) exceeded. Stopping retries.`,
            );

            const messageCard = createAlertMessageCard(
              platformEventName,
              currentState.lastReplayId,
            );

            await this.sendWebhookNotification(messageCard);

            console.log('Exiting process to allow ECS to restart the task...');
            process.exit(1); // Exit with error code 1 to indicate failure
          }
        }
      };

      await this.platformEventService.gRCPPlatformEventListener(
        platformEventName,
        subscribeCallback,
        state.lastReplayId,
      );
    } catch (error) {
      console.error(
        `Critical error in application change listener for ${platformEventName}:`,
        error,
      );

      // Stop monitoring
      this.stopMonitoring(platformEventName);
    }
  }

  async publishMessageToSNS(eventData: any) {
    try {
      if (eventData.event.replayId > this.lastProcessedReplayId) {
        // Store event in DynamoDB
        await this.dynamodbService.updateObject(
          process.env.OAP_EVENT_TABLE_NAME,
          {
            PK: eventData.event.EventApiName,
          },
          {
            replayId: eventData.event.replayId,
            event: eventData.event,
            createdDate: new Date(eventData.payload.CreatedDate).toISOString(),
            expirationDate: new Date(
              eventData.payload.CreatedDate + 24 * 60 * 60 * 1000,
            ).toISOString(),
          },
        );

        // Extract brand from payload and create filter
        const brand = eventData.payload.BusinessUnitFilter__c || 'HZU';

        // Publish to SNS topic
        const publishMessageResponse = await this.snsService.publishMessages(
          eventData,
          eventData.payload.Opportunity_Id__c,
          process.env.GUS_OAP_SF_TOPIC_ARN,
          `CR_${brand}`,
        );

        this.lastProcessedReplayId = eventData.event.replayId;

        console.log(`Last processed replayId: ${this.lastProcessedReplayId}`);
        console.log(
          'Publish Message Response -->',
          JSON.stringify(publishMessageResponse),
        );

        if (publishMessageResponse.MessageId) {
          await this.cloudWatchLoggerService.log(
            eventData.event?.EventUuid,
            new Date().toISOString(),
            this.loggerEnum.Component.GUS_SALESFORCE_EVENTS_LISTENER,
            this.loggerEnum.Component.GUS_SALESFORCE,
            'APPLICATION_CHANGE_NOTIFICATION',
            this.loggerEnum.Event.SYNC_APPLICATION_DETAILS,
            eventData.payload.Scenario__c || 'APPLICATION_CHANGE',
            eventData,
            eventData,
            'Message published to SNS',
            eventData.payload?.BusinessUnitFilter__c || '',
            eventData.payload?.Email__c || '',
            `gus-salesforce-event-listener/${eventData.event?.EventUuid}/${eventData.payload?.Email__c || 'no-email'}`,
            'email',
            eventData.payload?.Email__c || 'no-email',
            'Application_Change_Notification',
            '',
            'Application_Change',
            eventData.payload?.Email__c || 'no-email',
            publishMessageResponse,
          );
        }
      } else {
        console.log(
          `Skipping event with replayId: ${eventData.event.replayId}`,
        );
      }
    } catch (error) {
      console.error('Error in publishing to SNS:', error);
      await this.cloudWatchLoggerService.error(
        eventData.event?.EventUuid,
        new Date().toISOString(),
        'GUS_SALESFORCE_EVENTS_LISTENER',
        'GUS_SALESFORCE',
        'APPLICATION_CHANGE_NOTIFICATION',
        'SYNC_APPLICATION_DETAILS',
        'APPLICATION_CHANGE',
        eventData,
        eventData,
        error.message ? error.message : error,
        eventData.payload?.BusinessUnitFilter__c || '',
        eventData.payload?.Email__c || '',
        `gus-salesforce-event-listener/${eventData.event?.EventUuid}/${eventData.payload?.Email__c || 'no-email'}`,
        'email',
        eventData.payload?.Email__c || 'no-email',
        'Application_Change_Notification',
        '',
        'Application_Change',
        eventData.payload?.Email__c || 'no-email',
      );
    }
  }
}
