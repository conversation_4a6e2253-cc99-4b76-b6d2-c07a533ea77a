import { Controller, Get } from '@nestjs/common';
import { PlatformEventSubscriberService } from './service';

@Controller('')
export class PlatformEventSubscriberController {
  constructor(
    private readonly platformEventSubscriberService: PlatformEventSubscriberService,
  ) { }

  @Get('platformevent/listener')
  async platformEvent(): Promise<any> {
    try {
      return await this.platformEventSubscriberService.platformlistener(
        'Lead_Owner_Change_Event__e',
      );
    } catch (error) {
      console.log('Error', error);
      return {
        status: 'error',
        message: 'Failed to process platform event',
        details: error.message,
      };
    }
  }

  @Get('platformevent/application-change')
  async applicationChangeEvent(): Promise<any> {
    try {
      return await this.platformEventSubscriberService.applicationChangeListener(
        'Application_Change_Notification__e',
      );
    } catch (error) {
      console.log('Error', error);
      return {
        status: 'error',
        message: 'Failed to process application change event',
        details: error.message,
      };
    }
  }
}
