import { Controller, Get, Res } from '@nestjs/common';
import { AppService } from './app.service';
import type { Response } from 'express';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) { }

  @Get('/health')
  async healthCheck(@Res({ passthrough: true }) res: Response): Promise<any> {
    try {
      res.status(200).json({ statusCode: 200, message: 'SUCCESS' });
    } catch (error) {
      console.log('Error', error);
    }
  }
}