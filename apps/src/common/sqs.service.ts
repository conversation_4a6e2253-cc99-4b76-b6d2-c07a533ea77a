import { Injectable } from '@nestjs/common';
import { SQS } from 'aws-sdk';

@Injectable()
export class SqsService {
  private readonly sqs: SQS;

  constructor() {
    this.sqs = new SQS({
      region: process.env.REGION,
    });
  }

  async sendMessage(
    messageData: any,
    queueUrl: string,
    delaySeconds: number = 0,
    messageGroupId?: string,
  ): Promise<SQS.SendMessageResult> {
    const params: SQS.SendMessageRequest = {
      QueueUrl: queueUrl,
      MessageBody: JSON.stringify(messageData),
      DelaySeconds: delaySeconds,
    };

    // For FIFO queues, add MessageGroupId
    if (messageGroupId) {
      params.MessageGroupId = messageGroupId;
    }

    try {
      const response = await this.sqs.sendMessage(params).promise();
      return response;
    } catch (err) {
      console.error('Error sending message to SQS:', err);
      throw new Error('Error sending message to SQS');
    }
  }

  async sendMessagesBatch(
    messagesData: any[],
    queueUrl: string,
    messageGroupId?: string,
  ): Promise<SQS.SendMessageBatchResult> {
    const entries: SQS.SendMessageBatchRequestEntry[] = messagesData.map(
      (message, index) => ({
        Id: `msg-${index}`,
        MessageBody: JSON.stringify(message),
        MessageGroupId: messageGroupId, // For FIFO queues
      }),
    );

    const params: SQS.SendMessageBatchRequest = {
      QueueUrl: queueUrl,
      Entries: entries,
    };

    try {
      const response = await this.sqs.sendMessageBatch(params).promise();
      return response;
    } catch (err) {
      console.error('Error sending messages batch to SQS:', err);
      throw new Error('Error sending messages batch to SQS');
    }
  }
}
