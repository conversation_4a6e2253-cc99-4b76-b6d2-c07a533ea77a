import { Injectable } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class GrpcSslFixService {
  private readonly logger = console;

  /**
   * Apply SSL fixes for gRPC connections
   * This method patches the environment to handle SSL certificate issues
   */
  applyGrpcSslFixes(): void {
    this.logger.log('Applying gRPC SSL fixes...');

    // 1. Set up proper SSL environment variables
    this.setupSslEnvironment();

    // 2. Patch the certifi module resolution if needed
    this.patchCertifiResolution();

    // 3. Set up gRPC SSL options
    this.setupGrpcSslOptions();

    this.logger.log('gRPC SSL fixes applied successfully');
  }

  private setupSslEnvironment(): void {
    // Only disable SSL verification in development if explicitly set
    if (process.env.NODE_ENV === 'dev' && process.env.NODE_TLS_REJECT_UNAUTHORIZED === '0') {
      this.logger.warn('SSL verification disabled for development environment');
      return;
    }

    // For production, ensure SSL verification is enabled
    if (process.env.NODE_ENV === 'prod') {
      process.env.NODE_TLS_REJECT_UNAUTHORIZED = '1';
      this.logger.log('SSL verification enabled for production environment');
    }

    // Set additional SSL options for gRPC
    process.env.GRPC_SSL_CIPHER_SUITES = 'HIGH:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!SRP:!CAMELLIA';
    process.env.GRPC_DEFAULT_SSL_ROOTS_FILE_PATH = this.findCertificateBundle();
  }

  private patchCertifiResolution(): void {
    try {
      // Try to resolve certifi package
      const certifiPath = this.resolveCertifiPath();
      if (certifiPath && fs.existsSync(certifiPath)) {
        this.logger.log(`Using certifi CA bundle: ${certifiPath}`);
        
        // Patch the require resolution for certifi
        const Module = require('module');
        const originalRequire = Module.prototype.require;
        
        Module.prototype.require = function(id: string) {
          if (id === 'certifi') {
            return certifiPath;
          }
          return originalRequire.apply(this, arguments);
        };
        
        return;
      }
    } catch (error) {
      this.logger.warn('Could not resolve certifi package, using system certificates');
    }

    // Fallback: use system certificates
    const systemCaPath = this.findSystemCaPath();
    if (systemCaPath) {
      this.logger.log(`Using system CA bundle: ${systemCaPath}`);
      
      // Create a mock certifi module that returns the system CA path
      const Module = require('module');
      const originalRequire = Module.prototype.require;
      
      Module.prototype.require = function(id: string) {
        if (id === 'certifi') {
          return systemCaPath;
        }
        return originalRequire.apply(this, arguments);
      };
    }
  }

  private resolveCertifiPath(): string | null {
    const possiblePaths = [
      // pnpm paths
      path.join(process.cwd(), 'node_modules/.pnpm/certifi@14.5.15/node_modules/certifi/cacert.pem'),
      path.join(process.cwd(), 'node_modules/.pnpm/certifi@*/node_modules/certifi/cacert.pem'),
      // npm paths
      path.join(process.cwd(), 'node_modules/certifi/cacert.pem'),
      // yarn paths
      path.join(process.cwd(), 'node_modules/certifi/cacert.pem'),
    ];

    for (const certPath of possiblePaths) {
      if (fs.existsSync(certPath)) {
        return certPath;
      }
    }

    // Try to find using glob pattern
    try {
      const glob = require('glob');
      const matches = glob.sync('node_modules/.pnpm/certifi@*/node_modules/certifi/cacert.pem', {
        cwd: process.cwd()
      });
      if (matches.length > 0) {
        return path.join(process.cwd(), matches[0]);
      }
    } catch (error) {
      // glob not available, continue with other methods
    }

    return null;
  }

  private findSystemCaPath(): string | null {
    const possiblePaths = [
      '/etc/ssl/certs/ca-certificates.crt', // Debian/Ubuntu
      '/etc/pki/tls/certs/ca-bundle.crt',   // RHEL/CentOS
      '/etc/ssl/ca-bundle.pem',             // OpenSUSE
      '/etc/ssl/cert.pem',                  // macOS
      '/usr/local/share/certs/ca-root-nss.crt', // FreeBSD
    ];

    for (const caPath of possiblePaths) {
      if (fs.existsSync(caPath)) {
        return caPath;
      }
    }

    return null;
  }

  private findCertificateBundle(): string {
    // Try certifi first
    const certifiPath = this.resolveCertifiPath();
    if (certifiPath) {
      return certifiPath;
    }

    // Fallback to system certificates
    const systemPath = this.findSystemCaPath();
    if (systemPath) {
      return systemPath;
    }

    // Last resort: use Node.js built-in certificates
    return '';
  }

  private setupGrpcSslOptions(): void {
    // Set gRPC-specific SSL options
    const grpcSslOptions = {
      'grpc.ssl_target_name_override': undefined,
      'grpc.default_authority': undefined,
      'grpc.ssl_session_cache': true,
    };

    // Store options for later use by gRPC client
    (global as any).GRPC_SSL_OPTIONS = grpcSslOptions;
  }

  /**
   * Get SSL configuration for manual gRPC client setup
   */
  getSslConfig(): { ca?: Buffer; rejectUnauthorized: boolean } {
    const certPath = this.findCertificateBundle();
    
    if (certPath && fs.existsSync(certPath)) {
      try {
        return {
          ca: fs.readFileSync(certPath),
          rejectUnauthorized: process.env.NODE_TLS_REJECT_UNAUTHORIZED !== '0'
        };
      } catch (error) {
        this.logger.error('Error reading certificate bundle:', error);
      }
    }

    return {
      rejectUnauthorized: process.env.NODE_TLS_REJECT_UNAUTHORIZED !== '0'
    };
  }
}
