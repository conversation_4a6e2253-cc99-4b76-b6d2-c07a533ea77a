// salesforce-auth.service.ts
import { Injectable } from '@nestjs/common';

@Injectable()
export class SalesforceAuthService {
  async authenticate(): Promise<string> {
    const url = this.prepareAuthUrl();
    console.log('url', url)
    const response = await fetch(url, {
      method: 'post',
    });

    const body = await response.json();

    console.log("body", body)
    const accessToken = body.access_token;
    return accessToken;
  }

  private prepareAuthUrl(): string {
    const url = new URL(`${process.env.GUS_SALESFORCE_URL}/services/oauth2/token`);

    console.log('URL -->', url)

    // const urlParams = {
    //   username: "<EMAIL>",
    //   password: "Herzing@20248JSRPKpu0hEOLcTnkSStlpwz", // Assuming salesforcePassword is available in config
    //   grant_type: 'password',
    //   client_id: "3MVG9I5UQ_0k_hTlxg9Ks4vR.y9A_7OVQjFs2WVLeuC4yCkJCjlyzTavFKb5xohv3kJ1soWp9E4PcoisP5F5W",
    //   client_secret: "366DCE5570DED4AF80E214F90595037F5D884D226D077FA1D202930BA37A9CEB",
    // };

    const urlParams = {
      username: process.env.GUS_SALESFORCE_USERNAME,
      password: process.env.GUS_SALESFORCE_PASSWORD, // Assuming salesforcePassword is available in config
      grant_type: 'password',
      client_id: process.env.GUS_SALESFORCE_CLIENT_ID,
      client_secret: process.env.GUS_SALESFORCE_CLIENT_SECRET,
    };

    url.search = new URLSearchParams(urlParams).toString();

    return url.toString();
  }
}
