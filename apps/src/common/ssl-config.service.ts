import { Injectable } from '@nestjs/common';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class SslConfigService {
  private readonly logger = console;

  /**
   * Get SSL configuration for gRPC connections
   * This method provides fallback options for SSL certificate issues
   */
  getSslConfig(): { rejectUnauthorized: boolean; ca?: Buffer } {
    try {
      // First, try to use system CA certificates
      const systemCaPath = this.getSystemCaPath();
      if (systemCaPath && fs.existsSync(systemCaPath)) {
        this.logger.log('Using system CA certificates for SSL');
        return {
          rejectUnauthorized: true,
          ca: fs.readFileSync(systemCaPath)
        };
      }

      // Fallback: Check if we're in a development environment
      if (process.env.NODE_ENV === 'dev' || process.env.NODE_ENV === 'development') {
        this.logger.warn('Development environment detected - SSL verification may be relaxed');
        // In development, we might want to be more lenient
        return {
          rejectUnauthorized: process.env.NODE_TLS_REJECT_UNAUTHORIZED !== '0'
        };
      }

      // Production fallback - use default system certificates
      this.logger.log('Using default SSL configuration');
      return {
        rejectUnauthorized: true
      };

    } catch (error) {
      this.logger.error('Error configuring SSL:', error);
      
      // Emergency fallback - log the issue but don't break the connection
      this.logger.warn('Falling back to default SSL configuration due to error');
      return {
        rejectUnauthorized: process.env.NODE_TLS_REJECT_UNAUTHORIZED !== '0'
      };
    }
  }

  /**
   * Get the system CA certificate path based on the operating system
   */
  private getSystemCaPath(): string | null {
    const possiblePaths = [
      '/etc/ssl/certs/ca-certificates.crt', // Debian/Ubuntu
      '/etc/pki/tls/certs/ca-bundle.crt',   // RHEL/CentOS
      '/etc/ssl/ca-bundle.pem',             // OpenSUSE
      '/etc/ssl/cert.pem',                  // macOS
      '/usr/local/share/certs/ca-root-nss.crt', // FreeBSD
    ];

    for (const caPath of possiblePaths) {
      if (fs.existsSync(caPath)) {
        return caPath;
      }
    }

    return null;
  }

  /**
   * Validate SSL configuration and log diagnostics
   */
  validateSslConfig(): void {
    this.logger.log('=== SSL Configuration Diagnostics ===');
    this.logger.log(`NODE_ENV: ${process.env.NODE_ENV}`);
    this.logger.log(`NODE_TLS_REJECT_UNAUTHORIZED: ${process.env.NODE_TLS_REJECT_UNAUTHORIZED}`);
    
    const systemCaPath = this.getSystemCaPath();
    if (systemCaPath) {
      this.logger.log(`System CA path found: ${systemCaPath}`);
      try {
        const stats = fs.statSync(systemCaPath);
        this.logger.log(`CA file size: ${stats.size} bytes`);
        this.logger.log(`CA file modified: ${stats.mtime}`);
      } catch (error) {
        this.logger.error(`Error reading CA file: ${error.message}`);
      }
    } else {
      this.logger.warn('No system CA path found');
    }

    // Check if certifi package is available
    try {
      const certifi = require('certifi');
      if (fs.existsSync(certifi)) {
        this.logger.log(`Certifi CA bundle found: ${certifi}`);
      } else {
        this.logger.warn(`Certifi CA bundle not found at: ${certifi}`);
      }
    } catch (error) {
      this.logger.warn('Certifi package not available');
    }

    this.logger.log('=== End SSL Diagnostics ===');
  }
}
