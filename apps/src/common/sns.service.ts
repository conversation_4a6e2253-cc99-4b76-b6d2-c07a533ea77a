import { Injectable } from '@nestjs/common';
import { SNS } from 'aws-sdk';

@Injectable()
export class SnsService {
    private readonly sns: SNS;

    constructor() {
        this.sns = new SNS({
            region: process.env.REGION,
        });
    }

    async publishMessages(
        messageData: any,
        messageGroupId: any,
        topicArn: string,
        brand: string
    ): Promise<any> {
        const messageAttributes: SNS.MessageAttributeMap = {
            source: {
                DataType: 'String',
                StringValue: brand,
            },
        };
        console.log("messageAttributes ->", messageAttributes)
        let response;

        try {
            if (Array.isArray(messageData)) {
                for (const message of messageData) {
                    response = await this.publishMessageToSNS(
                        topicArn,
                        message,
                        messageGroupId,
                        messageAttributes
                    );
                }
            } else {
                response = await this.publishMessageToSNS(
                    topicArn,
                    messageData,
                    messageGroupId,
                    messageAttributes
                );
            }

            return response;
        } catch (err) {
            console.error('Error publishing messages:', err);
            throw new Error('Error publishing messages');
        }
    }

    private async publishMessageToSNS(
        topicArn: string,
        message: any,
        messageGroupId: string,
        messageAttributes: SNS.MessageAttributeMap
    ): Promise<SNS.PublishResponse> {
        const params: SNS.PublishInput = {
            TopicArn: topicArn,
            Message: JSON.stringify(message),
            MessageGroupId: messageGroupId,
            MessageAttributes: messageAttributes,
        };

        try {
            const res = await this.sns.publish(params).promise();
            return res;
        } catch (err) {
            console.error('Error publishing message to SNS:', err);
            throw err;
        }
    }
}
