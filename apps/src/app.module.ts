import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { PlatformEventSubscriberModule } from './eventsSubscriber/module';
import { ConfigModule } from '@nestjs/config';
const ENV = process.env.NODE_ENV || 'dev';

@Module({
  imports: [
    PlatformEventSubscriberModule,
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ENV === 'prod' ? '.env' : `.env.${ENV}`,
    }),
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule { }
