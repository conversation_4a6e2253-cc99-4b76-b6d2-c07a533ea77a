import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { SslConfigService } from './common/ssl-config.service';
import { GrpcSslFixService } from './common/grpc-ssl-fix.service';

async function bootstrap() {
  // Apply gRPC SSL fixes before anything else
  const grpcSslFixService = new GrpcSslFixService();
  grpcSslFixService.applyGrpcSslFixes();

  // Run SSL diagnostics
  const sslConfigService = new SslConfigService();
  sslConfigService.validateSslConfig();

  const app = await NestFactory.create(AppModule);
  await app.listen(3003);
}
bootstrap();
