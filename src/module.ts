import { DynamicModule, Module } from '@nestjs/common';
import { PlatformEventModuleOptions } from './interfaces/platform.event.interface';
import { PlatformEventService } from './platform.event.service';
@Module({})
export class PlatformEventModule {
  public static forRoot(params: PlatformEventModuleOptions): DynamicModule {
    const PlatformEventListenerProvider = {
      provide: params.options,
      useClass: PlatformEventService,
    };
    const PlatformEventListenerProviders = {
      provide: params.options,
      useFactory: () =>
        new PlatformEventService(
          params.username,
          params.password,
          params.clientId,
          params.clientSecret,
          params.authUrl,
        ),
    };

    return {
      module: PlatformEventModule,
      exports: [PlatformEventListenerProvider, PlatformEventListenerProviders],
      providers: [
        PlatformEventListenerProvider,
        PlatformEventListenerProviders,
      ],
    };
  }
}
