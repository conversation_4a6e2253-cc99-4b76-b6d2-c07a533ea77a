import { Injectable } from '@nestjs/common';
import { SalesforceAuthService } from './salesforceAuth';
import * as lib from 'cometd';
const cometdReplayExtension = require('./cometd-replay-extension');
import { Subject } from 'rxjs';
import PubSubApiClient, { SubscriptionInfo } from 'salesforce-event-listener';
require('cometd-nodejs-client').adapt();

type SubscribeCallback = (
  subscription: SubscriptionInfo,
  callbackType: string,
  data?: any,
) => any;

@Injectable()
export class PlatformEventService {
  private cometd: lib.CometD;
  private username: string;
  private password: string;
  private clientId: string;
  private clientSecret: string;
  private authUrl: string;
  private eventSubject: Subject<any>;
  private eventReceptionTimeout: NodeJS.Timeout | null = null;
  private readonly RECEPTION_CHECK_INTERVAL = 1 * 60 * 60 * 1000;

  constructor(
    username: string,
    password: string,
    clientId: string,
    clientSecret: string,
    authUrl: string,
  ) {
    this.cometd = new lib.CometD();
    this.username = username;
    this.password = password;
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.authUrl = authUrl;
    this.eventSubject = new Subject<any>();
  }

  async platformEventListener(platformEventName: string): Promise<void> {
    const authService = new SalesforceAuthService();
    const authToken = await authService.authenticate(
      this.authUrl,
      this.username,
      this.password,
      this.clientId,
      this.clientSecret,
    );

    const replayExtension = new cometdReplayExtension();
    replayExtension.setReplay('-1');
    replayExtension.setChannel(`/event/${platformEventName}`);

    this.cometd.configure({
      appendMessageTypeToURL: false,
      url: `${this.authUrl}/cometd/60.0`,
      requestHeaders: {
        Authorization: `Bearer ${authToken}`,
      },
    });
    await new Promise<void>((resolve, reject) => {
      console.log('Initiating handshake ...');
      this.cometd.handshake((handshake) => {
        console.log('Handshake message ', handshake);
        if (!handshake.successful) {
          console.error('Handshake failed', handshake);
        } else {
          console.log('Handshake successful');
          this.cometd.subscribe(`/event/${platformEventName}`, (message) => {
            (async () => {
              console.log(JSON.stringify(message));
              this.eventSubject.next(message);
            })().catch((error) => {
              console.error('Error in subscription callback:', error);
            });
          });
          resolve();
        }
      });
    });
    this.addConnectionListeners(platformEventName);
  }

  public async disconnectCometD() {
    const status = await this.cometd.getStatus();
    if (status !== 'disconnected') {
      await new Promise<void>((resolve) => {
        this.cometd.disconnect(() => {
          console.log('Successfully disconnected CometD');
          resolve();
        });
      }).catch((error) => {
        console.error('Error during CometD disconnect:', error);
      });
    }
  }

  async handleSkippedPlatformEvents(
    platformEventName: string,
    replayId: number,
  ): Promise<void> {
    try {
      if (!replayId) {
        throw new Error(`No replayId found for ${platformEventName}`);
      }

      const authService = new SalesforceAuthService();
      const authToken = await authService.authenticate(
        this.authUrl,
        this.username,
        this.password,
        this.clientId,
        this.clientSecret,
      );

      const replayExtension = new cometdReplayExtension();
      replayExtension.setReplay(replayId);
      replayExtension.setChannel(`/event/${platformEventName}`);

      await this.disconnectCometD();

      this.cometd.configure({
        appendMessageTypeToURL: false,
        url: `${this.authUrl}/cometd/60.0/`,
        requestHeaders: {
          Authorization: `Bearer ${authToken}`,
        },
      });

      this.cometd.registerExtension(`${platformEventName}`, replayExtension);

      await new Promise<void>((resolve, reject) => {
        console.log('Initiating handshake ...');
        this.cometd.handshake((handshake) => {
          console.log('Handshake message ', handshake);
          if (!handshake.successful) {
            console.error('Handshake failed', handshake);
            // reject(new Error('Handshake failed'));
          } else {
            console.log('Handshake successful');
            this.cometd.subscribe(`/event/${platformEventName}`, (message) => {
              (async () => {
                console.log(JSON.stringify(message));
                this.eventSubject.next(message);
              })().catch((error) => {
                console.error('Error in subscription callback:', error);
              });
            });
            resolve();
          }
        });
      });

      this.addConnectionListeners(platformEventName, replayId);
      this.resetReceptionTimeout(platformEventName);
    } catch (error) {
      console.error('Error in handleSkippedPlatformEvents:', error);
    }
  }

  private resetReceptionTimeout(platformEventName: string) {
    if (this.eventReceptionTimeout) {
      clearTimeout(this.eventReceptionTimeout);
    }
    this.eventReceptionTimeout = setTimeout(async () => {
      console.warn(
        `No events received for ${
          this.RECEPTION_CHECK_INTERVAL / 1000
        } seconds. Restarting subscription.`,
      );

      await this.disconnectCometD();
      console.log('Calling platform event listener ....');
      this.platformEventListener(platformEventName).catch((error) => {
        console.error('Error restarting platform event listener:', error);
      });
    }, this.RECEPTION_CHECK_INTERVAL);
  }

  addConnectionListeners(
    platformEventName: string,
    replayId: number = -1,
  ): void {
    // this.cometd.addListener('/meta/disconnect', () => {
    //   console.warn('Disconnected from CometD server. Reconnecting...');
    //   this.handleSkippedPlatformEvents(platformEventName, replayId);
    // });

    this.cometd.addListener('/meta/connect', async (message) => {
      if (message.successful) {
        console.log('Connected to CometD server');
      } else {
        console.error('Failed to connect to CometD server', message);
        if (message.advice) {
          const reconnect = message.advice.reconnect;
          if (reconnect === 'retry' || reconnect === 'none') {
            console.warn('Reconnecting to CometD server...');
            await this.platformEventListener(platformEventName);
          }
        }
      }
    });
  }

  getEventStream() {
    return this.eventSubject.asObservable();
  }

  async gRCPPlatformEventListener(
    platformEventName: string,
    subscribeCallback: SubscribeCallback,
    replayId?: number,
  ) {
    const client = new PubSubApiClient({
      authType: 'username-password',
      loginUrl: this.authUrl,
      clientId: this.clientId,
      clientSecret: this.clientSecret,
      username: this.username,
      password: this.password,
    });

    try {
      await client.connect();

      if (replayId) {
        client.subscribeFromReplayId(
          `/event/${platformEventName}`,
          subscribeCallback,
          null,
          replayId,
        );
      } else {
        client.subscribe(`/event/${platformEventName}`, subscribeCallback);
      }
    } catch (error) {
      console.error('Error when receiving events from gRCP', error);
      subscribeCallback(null, 'end');
      subscribeCallback(null, 'error', error);
    }
  }
}
