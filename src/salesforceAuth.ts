// salesforce-auth.service.ts
import { Injectable } from '@nestjs/common';

@Injectable()
export class SalesforceAuthService {
  async authenticate(authUrl, username, password, consumerKey, consumerSecret): Promise<string> {
    const url = this.prepareAuthUrl(authUrl, username, password, consumerKey, consumerSecret);
    const response = await fetch(url, {
      method: 'post',
    });

    const body = await response.json();

    const accessToken = body.access_token;
    return accessToken;
  }

  private prepareAuthUrl(authUrl, username, password, consumerKey, consumerSecret): string {
    const url = new URL(`${authUrl}/services/oauth2/token`);

    const urlParams = {
      username: username,
      password: password,
      grant_type: 'password',
      client_id: consumerKey,
      client_secret: consumerSecret,
    };

    url.search = new URLSearchParams(urlParams).toString();

    return url.toString();
  }
}
