export interface ILogger {
  log(
    requestedId: string,
    timestamp: string,
    component: string,
    source: string,
    destination: string,
    event: string,
    usecase: string,
    sourcePayload: any,
    destinationPayload: any,
    logMessage: any,
    brand: any,
    secondaryKey: string,
    logStreamName: string,
    destinationObjectType?: string,
    destinationObjectId?: string,
    sourceObjectType?: string,
    sourceObjectId?: string,
  ): Promise<any>;

  error(
    requestedId: string,
    timestamp: string,
    component: string,
    source: string,
    destination: string,
    event: string,
    usecase: string,
    sourcePayload: any,
    destinationPayload: any,
    errorMessage: any,
    brand: any,
    secondaryKey: string,
    logStreamName: string,
    destinationObjectType?: string,
    destinationObjectId?: string,
    sourceObjectType?: string,
    sourceObjectId?: string,
  ): Promise<any>;
}
