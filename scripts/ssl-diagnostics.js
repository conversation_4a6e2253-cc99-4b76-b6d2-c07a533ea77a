#!/usr/bin/env node

/**
 * SSL Diagnostics Script
 * This script helps diagnose SSL certificate issues with Salesforce Pub/Sub API
 */

const fs = require('fs');
const https = require('https');
const { execSync } = require('child_process');

console.log('=== SSL Diagnostics for Salesforce Pub/Sub API ===\n');

// 1. Check Node.js version and SSL support
console.log('1. Node.js Environment:');
console.log(`   Node.js version: ${process.version}`);
console.log(`   Platform: ${process.platform}`);
console.log(`   Architecture: ${process.arch}`);
console.log(`   NODE_TLS_REJECT_UNAUTHORIZED: ${process.env.NODE_TLS_REJECT_UNAUTHORIZED || 'undefined'}\n`);

// 2. Check system CA certificates
console.log('2. System CA Certificates:');
const possibleCaPaths = [
  '/etc/ssl/certs/ca-certificates.crt', // Debian/Ubuntu
  '/etc/pki/tls/certs/ca-bundle.crt',   // RHEL/CentOS
  '/etc/ssl/ca-bundle.pem',             // OpenSUSE
  '/etc/ssl/cert.pem',                  // macOS
  '/usr/local/share/certs/ca-root-nss.crt', // FreeBSD
];

let systemCaFound = false;
for (const caPath of possibleCaPaths) {
  if (fs.existsSync(caPath)) {
    console.log(`   ✓ Found system CA bundle: ${caPath}`);
    try {
      const stats = fs.statSync(caPath);
      console.log(`     Size: ${stats.size} bytes, Modified: ${stats.mtime.toISOString()}`);
      systemCaFound = true;
    } catch (error) {
      console.log(`     ✗ Error reading CA file: ${error.message}`);
    }
    break;
  }
}

if (!systemCaFound) {
  console.log('   ✗ No system CA bundle found in standard locations');
}
console.log();

// 3. Check certifi package
console.log('3. Certifi Package:');
try {
  const certifi = require('certifi');
  if (fs.existsSync(certifi)) {
    const stats = fs.statSync(certifi);
    console.log(`   ✓ Certifi CA bundle found: ${certifi}`);
    console.log(`     Size: ${stats.size} bytes, Modified: ${stats.mtime.toISOString()}`);
  } else {
    console.log(`   ✗ Certifi CA bundle not found at: ${certifi}`);
  }
} catch (error) {
  console.log(`   ✗ Certifi package not available: ${error.message}`);
}
console.log();

// 4. Test HTTPS connection to Salesforce
console.log('4. HTTPS Connection Test:');
const testUrls = [
  'https://api.pubsub.salesforce.com',
  'https://iapro.my.salesforce.com',
  'https://iapro--prodcopy.sandbox.my.salesforce.com'
];

async function testHttpsConnection(url) {
  return new Promise((resolve) => {
    const options = {
      hostname: new URL(url).hostname,
      port: 443,
      path: '/',
      method: 'GET',
      timeout: 5000
    };

    const req = https.request(options, (res) => {
      console.log(`   ✓ ${url}: Status ${res.statusCode}, TLS ${res.socket.getProtocol()}`);
      resolve(true);
    });

    req.on('error', (error) => {
      console.log(`   ✗ ${url}: ${error.message}`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log(`   ✗ ${url}: Connection timeout`);
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// Test connections
(async () => {
  for (const url of testUrls) {
    await testHttpsConnection(url);
  }
  
  console.log();
  
  // 5. OpenSSL information
  console.log('5. OpenSSL Information:');
  try {
    const opensslVersion = execSync('openssl version', { encoding: 'utf8' }).trim();
    console.log(`   OpenSSL version: ${opensslVersion}`);
  } catch (error) {
    console.log(`   ✗ OpenSSL not available: ${error.message}`);
  }
  
  // 6. Certificate chain test
  console.log('\n6. Certificate Chain Test:');
  try {
    const certChain = execSync('openssl s_client -connect api.pubsub.salesforce.com:7443 -servername api.pubsub.salesforce.com < /dev/null 2>/dev/null | openssl x509 -noout -subject -issuer', { encoding: 'utf8' });
    console.log(`   Certificate info:\n${certChain}`);
  } catch (error) {
    console.log(`   ✗ Could not retrieve certificate chain: ${error.message}`);
  }
  
  console.log('\n=== Recommendations ===');
  
  if (!systemCaFound) {
    console.log('• Install CA certificates in your container:');
    console.log('  RUN apt-get update && apt-get install -y ca-certificates && update-ca-certificates');
  }
  
  console.log('• For development/testing, you can temporarily disable SSL verification:');
  console.log('  export NODE_TLS_REJECT_UNAUTHORIZED=0');
  console.log('  WARNING: Never use this in production!');
  
  console.log('• For production, ensure your container has updated CA certificates');
  console.log('• Check if you\'re behind a corporate firewall that intercepts SSL');
  
  console.log('\n=== End Diagnostics ===');
})();
