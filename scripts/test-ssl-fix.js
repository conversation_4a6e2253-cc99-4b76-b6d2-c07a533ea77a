#!/usr/bin/env node

/**
 * Test SSL Fix Script
 * This script tests the SSL fixes without starting the full application
 */

const { GrpcSslFixService } = require('../dist/common/grpc-ssl-fix.service');
const { SslConfigService } = require('../dist/common/ssl-config.service');

console.log('=== Testing SSL Fixes ===\n');

try {
  // Test the gRPC SSL fix service
  console.log('1. Testing GrpcSslFixService...');
  const grpcSslFixService = new GrpcSslFixService();
  grpcSslFixService.applyGrpcSslFixes();
  
  const sslConfig = grpcSslFixService.getSslConfig();
  console.log('   SSL Config:', {
    hasCa: !!sslConfig.ca,
    caSize: sslConfig.ca ? sslConfig.ca.length : 0,
    rejectUnauthorized: sslConfig.rejectUnauthorized
  });
  
  console.log('   ✓ GrpcSslFixService working correctly\n');
  
  // Test the SSL config service
  console.log('2. Testing SslConfigService...');
  const sslConfigService = new SslConfigService();
  sslConfigService.validateSslConfig();
  
  const config = sslConfigService.getSslConfig();
  console.log('   SSL Config:', {
    hasCa: !!config.ca,
    caSize: config.ca ? config.ca.length : 0,
    rejectUnauthorized: config.rejectUnauthorized
  });
  
  console.log('   ✓ SslConfigService working correctly\n');
  
  // Test environment variables
  console.log('3. Environment Variables:');
  console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);
  console.log(`   NODE_TLS_REJECT_UNAUTHORIZED: ${process.env.NODE_TLS_REJECT_UNAUTHORIZED || 'undefined'}`);
  console.log(`   GRPC_SSL_CIPHER_SUITES: ${process.env.GRPC_SSL_CIPHER_SUITES || 'undefined'}`);
  console.log(`   GRPC_DEFAULT_SSL_ROOTS_FILE_PATH: ${process.env.GRPC_DEFAULT_SSL_ROOTS_FILE_PATH || 'undefined'}`);
  
  console.log('\n=== SSL Fixes Test Completed Successfully ===');
  
} catch (error) {
  console.error('❌ SSL Fixes Test Failed:', error);
  process.exit(1);
}
