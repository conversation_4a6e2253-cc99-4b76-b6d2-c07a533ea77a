REGION=eu-west-1
STAGE=dev
OAP_EVENT_TABLE_NAME=oap-events-dev
GUS_SF_QUEUE_URL=https://sqs.eu-west-1.amazonaws.com/933713876074/DEV-STUDENT-OAP-LEAD-OWNER-CHANGE-QUEUE.fifo
GUS_OAP_SF_TOPIC_ARN=arn:aws:sns:eu-west-1:933713876074:DEV-GUS-OAP-SF-TOPIC.fifo
GUS_SALESFORCE_URL=https://iapro--prodcopy.sandbox.my.salesforce.com
DOMAIN=https://iapro--prodcopy.sandbox.my.salesforce.com
API_VERSION=60.0
GUS_SALESFORCE_USERNAME=<EMAIL>
GUS_SALESFORCE_PASSWORD=5@{`bdT!!WfKsF5WeAtzaIddyBVMFP5rs8
GUS_SALESFORCE_CLIENT_ID=3MVG9I5UQ_0k_hTlxg9Ks4vR.y9A_7OVQjFs2WVLeuC4yCkJCjlyzTavFKb5xohv3kJ1soWp9E4PcoisP5F5W
GUS_SALESFORCE_CLIENT_SECRET=****************************************************************
LOGGER_LOG_GROUP_NAME=oap-loggers-dev
TEAMS_WEBHOOK_URL=https://gus.webhook.office.com/webhookb2/915b40cd-36b9-407e-acfe-0c8902cda66a@5665ee7a-3634-4975-9c21-2778cda48edd/IncomingWebhook/6b1a4014ad054be892840c3b0f3154c9/d3348445-411e-47c1-bce3-f0508548f373/V2DWSnqkBZcr_iRiRZb1b3r08QQO3rE9VCfQacX9Tknn01
TEAMS_HEALTH_WEBHOOK_URL=https://gus.webhook.office.com/webhookb2/eeaeed8b-07c4-4a4a-9afa-5938131ba922@5665ee7a-3634-4975-9c21-2778cda48edd/IncomingWebhook/5482a040951248b5bce8ef346940da3b/d3348445-411e-47c1-bce3-f0508548f373/V2yPpj-qCjKeAEj3wrj0fthBt1eu1f-yajVH8NuQtExks1

# SSL Configuration - Temporary fix for certificate issues
# WARNING: Only use in development/testing - not recommended for production
NODE_TLS_REJECT_UNAUTHORIZED=0