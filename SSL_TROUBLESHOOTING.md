# SSL Certificate Troubleshooting Guide

This guide helps resolve SSL certificate issues with the Salesforce Pub/Sub API connection.

## Problem Description

The application fails to connect to Salesforce Pub/Sub API with the error:
```
gRPC stream error: {"code":14,"details":"No connection established. Last error: Error: unable to get local issuer certificate"}
```

## Root Cause

The gRPC client cannot verify the SSL certificate chain when connecting to `api.pubsub.salesforce.com:7443`. This typically happens due to:

1. Missing or outdated CA certificates in the container
2. Issues with the `certifi` package resolution
3. Corporate firewall intercepting SSL connections
4. Container environment lacking proper certificate bundles

## Solutions

### 1. Quick Fix for Development (NOT for Production)

Add to your `.env.dev` file:
```bash
NODE_TLS_REJECT_UNAUTHORIZED=0
```

**⚠️ WARNING: Never use this in production as it disables SSL verification!**

### 2. Recommended Production Fix

The application now includes automatic SSL fixes:

- **Dockerfile Updated**: Installs CA certificates in the container
- **SSL Fix Service**: Automatically resolves certificate bundle issues
- **Environment Detection**: Applies appropriate fixes based on environment

### 3. Manual Container Fix

If you need to manually fix the container:

```dockerfile
# Add to your Dockerfile
RUN apt-get update && apt-get install -y ca-certificates && update-ca-certificates && rm -rf /var/lib/apt/lists/*
```

### 4. Verify Certificate Bundle

Run the SSL diagnostics script:
```bash
npm run ssl-diagnostics
```

This will check:
- Node.js environment
- System CA certificates
- Certifi package availability
- HTTPS connectivity to Salesforce
- Certificate chain information

## Environment Variables

### Development (.env.dev)
```bash
# Temporary SSL fix for development
NODE_TLS_REJECT_UNAUTHORIZED=0
```

### Production (.env)
```bash
# Keep SSL verification enabled
# NODE_TLS_REJECT_UNAUTHORIZED=1
```

## Troubleshooting Steps

### Step 1: Run Diagnostics
```bash
npm run ssl-diagnostics
```

### Step 2: Check Container Certificates
If running in Docker:
```bash
docker exec -it <container> ls -la /etc/ssl/certs/
docker exec -it <container> cat /etc/ssl/certs/ca-certificates.crt | head -10
```

### Step 3: Test gRPC Connection
```bash
# Test if the endpoint is reachable
openssl s_client -connect api.pubsub.salesforce.com:7443 -servername api.pubsub.salesforce.com
```

### Step 4: Check Application Logs
Look for these log messages:
- "Applying gRPC SSL fixes..."
- "Using certifi CA bundle: ..."
- "Using system CA bundle: ..."
- SSL configuration diagnostics

## Corporate Network Issues

If you're behind a corporate firewall:

1. **Check proxy settings**: Ensure HTTPS_PROXY is configured
2. **Certificate inspection**: Corporate firewalls may use custom certificates
3. **Whitelist domains**: Ensure `api.pubsub.salesforce.com` is whitelisted
4. **Port access**: Verify port 7443 is accessible

## Monitoring and Alerts

The application includes:
- **Automatic retry logic**: 5 retry attempts with exponential backoff
- **Health monitoring**: gRPC keep-alive monitoring
- **Teams notifications**: Alerts sent to configured webhook URLs
- **Graceful degradation**: Process exits for ECS restart on persistent failures

## Files Modified

1. **Dockerfile**: Added CA certificate installation
2. **main.ts**: Added SSL fix application on startup
3. **ssl-config.service.ts**: SSL configuration diagnostics
4. **grpc-ssl-fix.service.ts**: Automatic SSL issue resolution
5. **Environment files**: Added SSL configuration options
6. **scripts/ssl-diagnostics.js**: Comprehensive SSL diagnostics

## Testing the Fix

1. **Build the application**:
   ```bash
   npm run build
   ```

2. **Run locally with SSL diagnostics**:
   ```bash
   npm run ssl-diagnostics
   npm start
   ```

3. **Check logs for SSL fix messages**:
   - Look for "gRPC SSL fixes applied successfully"
   - Verify certificate bundle path is logged
   - Monitor for successful Salesforce connections

4. **Deploy to production**:
   - Ensure environment variables are properly set
   - Monitor application logs for SSL-related messages
   - Verify gRPC connections are established successfully

## Support

If issues persist:
1. Run `npm run ssl-diagnostics` and share the output
2. Check application logs for SSL-related errors
3. Verify network connectivity to Salesforce endpoints
4. Consider corporate network restrictions

## Security Notes

- Never disable SSL verification in production
- Keep CA certificates updated
- Monitor for certificate expiration
- Use proper environment-specific configurations
