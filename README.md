# @gus-eip/platform-listener

`@gus-eip/platform-listener` is a NestJS service for subscribing to Salesforce platform events using the CometD protocol. It handles authentication with Salesforce, establishes a connection to the CometD server, and provides an observable stream of received events.

## Installation

To install this package, use npm:

```bash
npm install @gus-eip/platform-listener rxjs
```

## Usage

### Import the Module

First, you need to import the `PlatformEventService` into your NestJS module.

```typescript
import { Module } from '@nestjs/common';
import { PlatformEventService } from '@gus-eip/platform-listener';

@Module({
  providers: [PlatformEventService],
  exports: [PlatformEventService],
})
export class AppModule {}
```

### Inject the Service

Inject the `PlatformEventService` into your controller or another service where you want to consume the events.

```typescript
import { Controller, OnModuleInit } from '@nestjs/common';
import { PlatformEventService } from '@gus-eip/platform-listener';

@Controller()
export class AppController implements OnModuleInit {
  constructor(private readonly platformEventService: PlatformEventService) {}

  async onModuleInit() {
    // Initialize the platform listener
    await this.platformEventService.platformListener('YourPlatformEventName');

    // Subscribe to the event stream
    this.platformEventService.getEventStream().subscribe((message) => {
      console.log('Received event:', message);
      // Process the event here
    });
  }
}
```

### Configuration

When creating an instance of the `PlatformEventService`, you need to provide the necessary Salesforce credentials and configuration details.

```typescript
import { PlatformEventService } from '@gus-eip/platform-listener';

const platformEventService = new PlatformEventService(
  'your-username',
  'your-password',
  'your-client-id',
  'your-client-secret',
  'your-auth-url',
  'your-event-table'
);
```

### Example

Here's a full example:

#### AppModule

```typescript
import { Module } from '@nestjs/common';
import { PlatformEventService } from '@gus-eip/platform-listener';
import { AppController } from './app.controller';

@Module({
  providers: [
    {
      provide: PlatformEventService,
      useFactory: () => {
        return new PlatformEventService(
          'your-username',
          'your-password',
          'your-client-id',
          'your-client-secret',
          'your-auth-url',
          'your-event-table'
        );
      },
    },
  ],
  controllers: [AppController],
  exports: [PlatformEventService],
})
export class AppModule {}
```

#### AppController

```typescript
import { Controller, OnModuleInit } from '@nestjs/common';
import { PlatformEventService } from '@gus-eip/platform-listener';

@Controller()
export class AppController implements OnModuleInit {
  constructor(private readonly platformEventService: PlatformEventService) {}

  async onModuleInit() {
    await this.platformEventService.platformListener('YourPlatformEventName');

    this.platformEventService.getEventStream().subscribe((message) => {
      console.log('Received event:', message);
      // Process the event here
    });
  }
}
```

## API

### `platformListener(platformEventName: string): Promise<void>`

Initializes the platform event listener for the specified event name.

- `platformEventName`: The name of the Salesforce platform event to subscribe to.

### `getEventStream()`

Returns an observable stream of received events. Other parts of the application can subscribe to this observable to receive real-time updates.

## License

MIT
```

This `README.md` file provides a clear guide on how to install, configure, and use the `@gus-eip/platform-listener` package in a NestJS application. It includes detailed instructions, example code, and API documentation.