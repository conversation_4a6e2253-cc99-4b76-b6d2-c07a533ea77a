FROM public.ecr.aws/docker/library/node:18

# Install CA certificates and update them
RUN apt-get update && apt-get install -y ca-certificates && update-ca-certificates && rm -rf /var/lib/apt/lists/*

WORKDIR /usr/src/app

COPY package*.json ./

RUN npm install -g pnpm

RUN pnpm install

COPY . .

COPY .env .env.dev ./

RUN pnpm run build

EXPOSE 3003

ARG NODE_ENV

# Set environment variable based on the build argument
ENV NODE_ENV=${NODE_ENV}

# Print NODE_ENV during build
RUN echo "NODE_ENV during build: ${NODE_ENV}"

# Use shell form to evaluate the environment variable and print it at runtime
CMD ["sh", "-c", "echo NODE_ENV at runtime: ${NODE_ENV} && pnpm start:${NODE_ENV}"]