{"name": "pubsub-listener", "version": "1.0.0", "type": "module", "description": "A node client for the Salesforce Pub/Sub API", "author": "GUS", "license": "CC0-1.0", "homepage": "https://github.com/pozil/pub-sub-api-node-client", "main": "./dist/client.js", "exports": {".": {"require": "./dist/client.cjs", "import": "./dist/client.js"}}, "scripts": {"build": "tsup && tsc", "test": "jasmine", "format": "prettier --write '**/*.{css,html,js,json,md,yaml,yml}'", "format:verify": "prettier --check '**/*.{css,html,js,json,md,yaml,yml}'", "lint": "eslint \"src/**\" \"spec/**\"", "prepare": "husky || true", "precommit": "lint-staged", "prepublishOnly": "npm run build"}, "dependencies": {"@grpc/grpc-js": "^1.12.2", "@grpc/proto-loader": "^0.7.13", "avro-js": "^1.12.0", "certifi": "^14.5.15", "jsforce": "^3.6.2", "undici": "^6.21.0"}, "devDependencies": {"@chialab/esbuild-plugin-meta-url": "^0.18.2", "dotenv": "^16.4.5", "eslint": "^9.14.0", "eslint-plugin-jasmine": "^4.2.2", "husky": "^9.1.6", "jasmine": "^5.4.0", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "tsup": "^8.3.5", "typescript": "^5.6.3"}, "lint-staged": {"**/*.{css,html,js,json,md,yaml,yml}": ["prettier --write"], "**/{src,spec}/**/*.js": ["eslint"]}, "keywords": ["salesforce", "pubsub", "api", "grpc"], "files": ["dist/*", "pubsub_api.proto"], "volta": {"node": "20.17.0"}}